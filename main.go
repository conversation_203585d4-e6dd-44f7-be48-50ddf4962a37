package main

import (
	"crypto/sha256"
	"crypto/subtle"
	"log"
	"os"

	"github.com/applegold/superbase-control/routes"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/keyauth"
	"github.com/joho/godotenv"
)

// loadEnv loads environment variables from .env file
func loadEnv() {
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found or could not be loaded: %v", err)
	}
}

func setUpRoutes(app *fiber.App) {
	// Load environment variables
	loadEnv()

	// Get API key from environment
	apiKey := os.Getenv("API_KEY")
	if apiKey == "" {
		log.Fatal("API_KEY environment variable is required")
	}

	authMiddleware := keyauth.New(keyauth.Config{
		Validator: func(c *fiber.Ctx, key string) (bool, error) {
			hashedAPIKey := sha256.Sum256([]byte(apiKey))
			hashedKey := sha256.Sum256([]byte(key))

			if subtle.ConstantTimeCompare(hashedAPIKey[:], hashedKey[:]) == 1 {
				return true, nil
			}
			return false, keyauth.ErrMissingOrMalformedAPIKey
		},
	})

	app.Get("/", routes.Hello)

	app.Get("/allowed", authMiddleware, routes.Allowed)

	app.Get("/self-help-tips", authMiddleware, routes.SelfHelpTips)

	app.Post("/add-mood", authMiddleware, routes.AddMood)

	app.Post("/add-journal-entry", authMiddleware, routes.AddJournalEntry)

}

func main() {
	app := fiber.New()

	setUpRoutes(app)

	log.Fatal(app.Listen(":3000"))
}
