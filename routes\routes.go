package routes

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/joho/godotenv"
)

// SelfHelpTip represents a self-help tip from the database
type SelfHelpTip struct {
	ID        int64  `json:"id"`
	Title     string `json:"title"`
	Desc      string `json:"desc"`
	CreatedAt string `json:"created_at"`
}

// loadEnv loads environment variables from .env file
func loadEnv() {
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found or could not be loaded: %v", err)
	}
}

func Hello(c *fiber.Ctx) error {
	return c.SendString("Hello, World!")
}

func Allowed(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func SelfHelpTips(c *fiber.Ctx) error {
	// Load environment variables
	loadEnv()

	// Get Supabase configuration from environment
	serviceRoleKey := os.Getenv("SERVICE_ROLE_KEY")
	serverURL := os.Getenv("SERVER_URL")

	if serviceRoleKey == "" || serverURL == "" {
		return c.Status(500).JSON(fiber.Map{
			"error": "Missing Supabase configuration",
		})
	}

	// Use RPC to call a PostgreSQL function that can access the moodvide schema
	apiURL := fmt.Sprintf("%srpc/get_self_help_tips", serverURL)

	// Create HTTP request
	req, err := http.NewRequest("POST", apiURL, nil)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set required headers for Supabase
	req.Header.Set("Authorization", "Bearer "+serviceRoleKey)
	req.Header.Set("apikey", serviceRoleKey)
	req.Header.Set("Content-Type", "application/json")

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch data from Supabase",
		})
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to read response",
		})
	}

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error": fmt.Sprintf("Supabase API error: %s", string(body)),
		})
	}

	// Parse JSON response
	var tips []SelfHelpTip
	if err := json.Unmarshal(body, &tips); err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse response",
		})
	}

	// Return the tips
	return c.JSON(fiber.Map{
		"success": true,
		"data":    tips,
		"count":   len(tips),
	})
}

// FlutterRequest represents the request structure from Flutter app
type FlutterRequest struct {
	Data MoodData `json:"data"`
}

// MoodData represents the mood data from Flutter app
type MoodData struct {
	Note           string `json:"note"`
	IsSharedWithAI bool   `json:"isSharedWithAI"`
	Rating         int    `json:"rating"`
	Mood           string `json:"mood"`
	UserID         string `json:"userId"`
}

// JournalEntry represents the journal entry data from Flutter app
type JournalEntry struct {
	Entry  string `json:"entry"`
	UserID string `json:"userId"`
}

func AddMood(c *fiber.Ctx) error {
	// Debug: Print raw request body
	rawBody := c.Body()
	log.Printf("=== Raw Request Body ===")
	log.Printf("Raw Body: %s", string(rawBody))
	log.Printf("Content-Type: %s", c.Get("Content-Type"))
	log.Printf("========================")

	// Parse the JSON body with Flutter structure
	var flutterRequest FlutterRequest
	if err := c.BodyParser(&flutterRequest); err != nil {
		log.Printf("Error parsing request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Invalid request body",
		})
	}

	// Extract the mood data from the Flutter request
	moodData := flutterRequest.Data

	// Print the received data to console
	log.Printf("=== Parsed Mood Data ===")
	log.Printf("Note: '%s'", moodData.Note)
	log.Printf("IsSharedWithAI: %t", moodData.IsSharedWithAI)
	log.Printf("Rating: %d", moodData.Rating)
	log.Printf("Mood: '%s'", moodData.Mood)
	log.Printf("UserID: '%s'", moodData.UserID)
	log.Printf("========================")

	// Load environment variables
	loadEnv()

	// Get Supabase configuration from environment
	serviceRoleKey := os.Getenv("SERVICE_ROLE_KEY")
	serverURL := os.Getenv("SERVER_URL")

	if serviceRoleKey == "" || serverURL == "" {
		return c.Status(500).JSON(fiber.Map{
			"error": "Missing Supabase configuration",
		})
	}

	// Create the data for the single JSON parameter function
	insertData := map[string]interface{}{
		"mood_data": map[string]interface{}{
			"user_id":           moodData.UserID,
			"mood":              moodData.Mood,
			"rating":            moodData.Rating,
			"is_shared_with_ai": moodData.IsSharedWithAI,
			"note":              moodData.Note,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(insertData)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare data",
		})
	}

	// Debug: Print what we're sending to Supabase
	log.Printf("=== Sending to Supabase ===")
	log.Printf("JSON Data: %s", string(jsonData))
	log.Printf("========================")

	// Insert into moodvide schema table
	apiURL := fmt.Sprintf("%srpc/insert_mood_entry", serverURL)

	// Create HTTP request
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(string(jsonData)))
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set required headers for Supabase
	req.Header.Set("Authorization", "Bearer "+serviceRoleKey)
	req.Header.Set("apikey", serviceRoleKey)
	req.Header.Set("Content-Type", "application/json")

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to save data to Supabase",
		})
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to read response",
		})
	}

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.Printf("Supabase error: %s", string(body))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error": fmt.Sprintf("Failed to save mood data: %s", string(body)),
		})
	}

	// Debug: Print Supabase response
	log.Printf("=== Supabase Response ===")
	log.Printf("Status: %d", resp.StatusCode)
	log.Printf("Response: %s", string(body))
	log.Printf("========================")

	log.Printf("Mood data saved successfully to database")

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Mood data saved successfully",
		"data":    moodData,
	})
}

func AddJournalEntry(c *fiber.Ctx) error {
	// Debug: Print raw request body
	rawBody := c.Body()
	log.Printf("=== Raw Journal Request Body ===")
	log.Printf("Raw Body: %s", string(rawBody))
	log.Printf("Content-Type: %s", c.Get("Content-Type"))
	log.Printf("===============================")

	// Parse the JSON body
	var journalEntry JournalEntry
	if err := c.BodyParser(&journalEntry); err != nil {
		log.Printf("Error parsing journal request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"success": false,
			"error":   "Invalid request body",
		})
	}

	// Print the received data to console
	log.Printf("=== Parsed Journal Entry ===")
	log.Printf("Entry: '%s'", journalEntry.Entry)
	log.Printf("UserID: '%s'", journalEntry.UserID)
	log.Printf("============================")

	// Load environment variables
	loadEnv()

	// Get Supabase configuration from environment
	serviceRoleKey := os.Getenv("SERVICE_ROLE_KEY")
	serverURL := os.Getenv("SERVER_URL")

	if serviceRoleKey == "" || serverURL == "" {
		return c.Status(500).JSON(fiber.Map{
			"error": "Missing Supabase configuration",
		})
	}

	// Create the data for the journal entry function
	insertData := map[string]interface{}{
		"journal_data": map[string]interface{}{
			"user_id": journalEntry.UserID,
			"entry":   journalEntry.Entry,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(insertData)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to prepare data",
		})
	}

	// Debug: Print what we're sending to Supabase
	log.Printf("=== Sending Journal Data to Supabase ===")
	log.Printf("JSON Data: %s", string(jsonData))
	log.Printf("=======================================")

	// Insert into moodvide schema table
	apiURL := fmt.Sprintf("%srpc/insert_journal_entry", serverURL)

	// Create HTTP request
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(string(jsonData)))
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set required headers for Supabase
	req.Header.Set("Authorization", "Bearer "+serviceRoleKey)
	req.Header.Set("apikey", serviceRoleKey)
	req.Header.Set("Content-Type", "application/json")

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to save journal entry to Supabase",
		})
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to read response",
		})
	}

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		log.Printf("Supabase journal entry error: %s", string(body))
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error": fmt.Sprintf("Failed to save journal entry: %s", string(body)),
		})
	}

	// Debug: Print Supabase response
	log.Printf("=== Supabase Journal Response ===")
	log.Printf("Status: %d", resp.StatusCode)
	log.Printf("Response: %s", string(body))
	log.Printf("================================")

	log.Printf("Journal entry saved successfully to database")

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Journal entry saved successfully",
		"data":    journalEntry,
	})
}
