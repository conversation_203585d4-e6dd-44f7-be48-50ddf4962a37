package routes

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"

	"github.com/gofiber/fiber/v2"
	"github.com/joho/godotenv"
)

// SelfHelpTip represents a self-help tip from the database
type SelfHelpTip struct {
	ID          int    `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Category    string `json:"category"`
	CreatedAt   string `json:"created_at"`
}

// loadEnv loads environment variables from .env file
func loadEnv() {
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found or could not be loaded: %v", err)
	}
}

func Hello(c *fiber.Ctx) error {
	return c.SendString("Hello, World!")
}

func Allowed(c *fiber.Ctx) error {
	return c.SendString("Successfully authenticated!")
}

func SelfHelpTips(c *fiber.Ctx) error {
	// Load environment variables
	loadEnv()

	// Get Supabase configuration from environment
	serviceRoleKey := os.Getenv("SERVICE_ROLE_KEY")
	serverURL := os.Getenv("SERVER_URL")

	if serviceRoleKey == "" || serverURL == "" {
		return c.Status(500).JSON(fiber.Map{
			"error": "Missing Supabase configuration",
		})
	}

	// Construct the API URL for the moodvide schema
	apiURL := fmt.Sprintf("%sself_help_tips?select=*", serverURL)

	// Create HTTP request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to create request",
		})
	}

	// Set required headers for Supabase
	req.Header.Set("Authorization", "Bearer "+serviceRoleKey)
	req.Header.Set("apikey", serviceRoleKey)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept-Profile", "moodvide")
	req.Header.Set("Content-Profile", "moodvide")

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch data from Supabase",
		})
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to read response",
		})
	}

	// Check if request was successful
	if resp.StatusCode != http.StatusOK {
		return c.Status(resp.StatusCode).JSON(fiber.Map{
			"error": fmt.Sprintf("Supabase API error: %s", string(body)),
		})
	}

	// Parse JSON response
	var tips []SelfHelpTip
	if err := json.Unmarshal(body, &tips); err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to parse response",
		})
	}

	// Return the tips
	return c.JSON(fiber.Map{
		"success": true,
		"data":    tips,
		"count":   len(tips),
	})
}
